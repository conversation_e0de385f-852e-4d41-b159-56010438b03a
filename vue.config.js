const fs = require('fs')
const path = require('path')

// 项目标识符 - 用于文件命名隔离
const PROJECT_ID = 'codepass-ruoyi'

module.exports = {
    css: {
        loaderOptions: {
            postcss: {
                plugins: [
                    require("tailwindcss"),
                    require("autoprefixer")
                ],
            },
        },
    },
    // 禁用生产环境的source map
    productionSourceMap: false,
    
    // 配置webpack
    configureWebpack: config => {
        if (process.env.NODE_ENV === 'production') {
            // 生产环境配置
            config.mode = 'production'
            
            // 禁用devtool，不生成.map文件
            config.devtool = false
            
            // 配置所有文件的命名格式，确保项目隔离
            config.output = {
                ...config.output,
                // JS文件命名：项目标识+功能+hash
                filename: `js/${PROJECT_ID}-app-[name]-[contenthash:10].js`,
                chunkFilename: `js/${PROJECT_ID}-chunk-[name]-[contenthash:10].js`
            }
            
            config.optimization = {
                ...config.optimization,
                minimize: true,
                // 代码分割配置
                splitChunks: {
                    chunks: 'all',
                    cacheGroups: {
                        // 第三方库
                        vendor: {
                            name: `${PROJECT_ID}-vendor-libs`,
                            test: /[\\/]node_modules[\\/]/,
                            priority: 10,
                            chunks: 'all'
                        },
                        // 公共代码
                        common: {
                            name: `${PROJECT_ID}-common-code`,
                            minChunks: 2,
                            priority: 5,
                            chunks: 'all',
                            reuseExistingChunk: true
                        }
                    }
                }
            }
        } else {
            // 开发环境配置 
            config.mode = 'development'
            config.devtool = 'source-map'
        }
    },
    
    // 链式配置
    chainWebpack: config => {
        if (process.env.NODE_ENV === 'production') {
            // 配置CSS文件名 - 对于uni-app项目，CSS可能通过不同的插件处理
            try {
                // 尝试多种CSS插件配置方式
                const cssPluginNames = ['extract-css', 'mini-css-extract-plugin', 'css-extract']
                
                cssPluginNames.forEach(pluginName => {
                    if (config.plugins.has(pluginName)) {
                        config.plugin(pluginName).tap(args => {
                            if (args[0]) {
                                args[0].filename = `css/${PROJECT_ID}-[name]-[contenthash:10].css`
                                args[0].chunkFilename = `css/${PROJECT_ID}-chunk-[name]-[contenthash:10].css`
                            }
                            return args
                        })
                    }
                })
            } catch (e) {
                // 如果CSS插件配置失败，忽略
            }
        }
    }
}
