const fs = require('fs')
const path = require('path')

module.exports = {
    css: {
        loaderOptions: {
            postcss: {
                plugins: [
                    require("tailwindcss"),
                    require("autoprefixer")
                ],
            },
        },
    },
    // 配置webpack
    configureWebpack: config => {
        if (process.env.NODE_ENV === 'production') {
            // 生产环境配置
            config.mode = 'production'
            config.optimization = {
                minimize: true,
                // 确保代码分割和chunk命名
                splitChunks: {
                    chunks: 'all',
                    cacheGroups: {
                        vendor: {
                            name: 'vendor',
                            test: /[\\/]node_modules[\\/]/,
                            priority: 10,
                            chunks: 'all'
                        },
                        common: {
                            name: 'common',
                            minChunks: 2,
                            priority: 5,
                            chunks: 'all',
                            reuseExistingChunk: true
                        }
                    }
                }
            }
            // 自定义输出文件名，使用简短且唯一的命名
            config.output = {
                ...config.output,
                filename: 'js/[name].[contenthash:8].js',
                chunkFilename: 'js/[name].[contenthash:8].js',
                // 确保资源文件也有合适的命名
                assetModuleFilename: 'assets/[name].[contenthash:8][ext]'
            }
            // 确保不生成source map文件
            config.devtool = false
        } else {
            // 开发环境配置
            config.mode = 'development'
            config.devtool = 'source-map'
        }
    }
}
